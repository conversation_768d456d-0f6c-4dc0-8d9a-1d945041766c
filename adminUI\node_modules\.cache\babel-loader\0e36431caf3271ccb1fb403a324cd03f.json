{"remainingRequest": "C:\\Users\\<USER>\\Desktop\\shop\\adminUI\\node_modules\\babel-loader\\lib\\index.js!C:\\Users\\<USER>\\Desktop\\shop\\adminUI\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!C:\\Users\\<USER>\\Desktop\\shop\\adminUI\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!C:\\Users\\<USER>\\Desktop\\shop\\adminUI\\src\\views\\systemSetting\\administratorAuthority\\identityManager\\index.vue?vue&type=script&lang=js", "dependencies": [{"path": "C:\\Users\\<USER>\\Desktop\\shop\\adminUI\\src\\views\\systemSetting\\administratorAuthority\\identityManager\\index.vue", "mtime": 1754557725824}, {"path": "C:\\Users\\<USER>\\Desktop\\shop\\adminUI\\babel.config.js", "mtime": 1754050582109}, {"path": "C:\\Users\\<USER>\\Desktop\\shop\\adminUI\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1754554386628}, {"path": "C:\\Users\\<USER>\\Desktop\\shop\\adminUI\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1754554387118}, {"path": "C:\\Users\\<USER>\\Desktop\\shop\\adminUI\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1754554386628}, {"path": "C:\\Users\\<USER>\\Desktop\\shop\\adminUI\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1754554388384}], "contextDependencies": [], "result": ["\"use strict\";\n\nfunction _typeof(o) { \"@babel/helpers - typeof\"; return _typeof = \"function\" == typeof Symbol && \"symbol\" == typeof Symbol.iterator ? function (o) { return typeof o; } : function (o) { return o && \"function\" == typeof Symbol && o.constructor === Symbol && o !== Symbol.prototype ? \"symbol\" : typeof o; }, _typeof(o); }\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports.default = void 0;\nvar roleApi = _interopRequireWildcard(require(\"@/api/role.js\"));\nvar _edit = _interopRequireDefault(require(\"./edit\"));\nvar _permission = require(\"@/utils/permission\");\nfunction _interopRequireDefault(e) { return e && e.__esModule ? e : { default: e }; }\nfunction _interopRequireWildcard(e, t) { if (\"function\" == typeof WeakMap) var r = new WeakMap(), n = new WeakMap(); return (_interopRequireWildcard = function _interopRequireWildcard(e, t) { if (!t && e && e.__esModule) return e; var o, i, f = { __proto__: null, default: e }; if (null === e || \"object\" != _typeof(e) && \"function\" != typeof e) return f; if (o = t ? n : r) { if (o.has(e)) return o.get(e); o.set(e, f); } for (var _t in e) \"default\" !== _t && {}.hasOwnProperty.call(e, _t) && ((i = (o = Object.defineProperty) && Object.getOwnPropertyDescriptor(e, _t)) && (i.get || i.set) ? o(f, _t, i) : f[_t] = e[_t]); return f; })(e, t); }\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n// 权限判断函数\nvar _default = exports.default = {\n  // name: \"index\"\n  components: {\n    edit: _edit.default\n  },\n  data: function data() {\n    return {\n      constants: this.$constants,\n      listData: {\n        list: []\n      },\n      listPram: {\n        createTime: null,\n        updateTime: null,\n        level: null,\n        page: 1,\n        limit: this.$constants.page.limit[0],\n        roleName: null,\n        rules: null,\n        status: null\n      },\n      menuList: [],\n      editDialogConfig: {\n        visible: false,\n        isCreate: 0,\n        // 0=创建，1=编辑\n        editData: {}\n      }\n    };\n  },\n  mounted: function mounted() {\n    this.handleGetRoleList();\n  },\n  methods: {\n    checkPermi: _permission.checkPermi,\n    handlerOpenDel: function handlerOpenDel(rowData) {\n      var _this = this;\n      this.$confirm(this.$t(\"admin.system.role.confirmDelete\")).then(function () {\n        roleApi.delRole(rowData).then(function (data) {\n          _this.$message.success(_this.$t(\"admin.system.role.deleteSuccess\"));\n          _this.handleGetRoleList();\n        });\n      });\n    },\n    handleGetRoleList: function handleGetRoleList() {\n      var _this2 = this;\n      roleApi.getRoleList(this.listPram).then(function (res) {\n        _this2.listData = res;\n      }).catch(function () {\n        _this2.$message.error(_this2.$t(\"common.fetchDataFailed\"));\n      });\n    },\n    handlerOpenEdit: function handlerOpenEdit(isCreate, editDate) {\n      isCreate === 1 ? this.editDialogConfig.editData = editDate : this.editDialogConfig.editData = {};\n      this.editDialogConfig.isCreate = isCreate;\n      this.editDialogConfig.visible = true;\n    },\n    hideEditDialog: function hideEditDialog() {\n      this.editDialogConfig.visible = false;\n      this.handleGetRoleList();\n    },\n    handleSizeChange: function handleSizeChange(val) {\n      this.listPram.limit = val;\n      this.handleGetRoleList(this.listPram);\n    },\n    handleCurrentChange: function handleCurrentChange(val) {\n      this.listPram.page = val;\n      this.handleGetRoleList(this.listPram);\n    },\n    //修改状态\n    handleStatusChange: function handleStatusChange(row) {\n      var _this3 = this;\n      roleApi.updateRoleStatus(row).then(function (res) {\n        _this3.$message.success(\"更新状态成功\");\n        _this3.handleGetRoleList();\n      });\n    },\n    resetQuery: function resetQuery() {\n      this.listPram.roleName = \"\";\n      this.handleGetRoleList();\n    }\n  }\n};", null]}